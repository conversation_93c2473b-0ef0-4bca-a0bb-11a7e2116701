package com.example.product_management_api.exception;

import com.example.product_management_api.utility.ErrorStructure;
import com.example.product_management_api.utility.FieldErrorStructure;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<FieldErrorStructure<Map<String, String>>> handleValidationExceptions(
            MethodArgumentNotValidException ex, WebRequest request) {
        
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });

        FieldErrorStructure<Map<String, String>> errorStructure = FieldErrorStructure.<Map<String, String>>builder()
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .errorMessage("Validation failed")
                .timestamp(LocalDateTime.now().toString())
                .path(request.getDescription(false).replace("uri=", ""))
                .data(errors)
                .build();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorStructure);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorStructure> handleIllegalArgumentException(
            IllegalArgumentException ex, WebRequest request) {
        
        ErrorStructure errorStructure = ErrorStructure.builder()
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .message(ex.getMessage())
                .timestamp(LocalDateTime.now().toString())
                .path(request.getDescription(false).replace("uri=", ""))
                .build();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorStructure);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorStructure> handleGlobalException(
            Exception ex, WebRequest request) {
        
        ErrorStructure errorStructure = ErrorStructure.builder()
                .statusCode(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .message("An unexpected error occurred: " + ex.getMessage())
                .timestamp(LocalDateTime.now().toString())
                .path(request.getDescription(false).replace("uri=", ""))
                .build();

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorStructure);
    }
}
