package com.example.product_management_api.utility;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RestResponse<T> {
    private LocalDateTime timestamp;
    private int statusCode;
    private HttpStatus status;
    private String message;
    private T data;
}
